#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
#End If

Public Sub CCC_Person_Import_v4(ByVal dfImport As DataFileImport)

	' Standard variables
	Dim logSection As IDisposable

	' Table
	Dim table = Connection.Tables("Person")

	' Line counter
	Dim counter As New LineCounter()

	' Value provider
	Dim lineData As New LineValueProvider(New String() {"personnelnumber", "firstname", "lastname", "salutation", "title", "preferredname", "personaltitle", "gender", "birthdate", "isinactive", "uid_department", "uid_profitcenter", "entrydate", "exitdate", "companymember", "uid_personhead", "uid_locality", "uid_dialogcountry", "phone", "phonemobile", "defaultemailaddress", "customproperty01", "customproperty02", "customproperty03", "customproperty04", "customproperty05", "customproperty06", "customproperty07", "TIMESTAMP"})
	Provider = lineData

	' Import from an external database
	Dim lineProvider As ILineProvider
	Dim dbLineProvider As New DbLineProvider()
	dbLineProvider.ProviderName = "System.Data.SqlClient.SqlClientFactory, System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
	dbLineProvider.ConnectionString = "Data Source=" & Connection.GetConfigParm("Custom\DB-RH\IP") & "," & Connection.GetConfigParm("Custom\DB-RH\Port") & ";Initial Catalog=" & Connection.GetConfigParm("Custom\DB-RH\DataBase") & ";User ID=" & Connection.GetConfigParm("Custom\DB-RH\User") & ";Password=" & Connection.GetConfigParm("Custom\DB-RH\Password") & ";Pooling=False"
	'dbLineProvider.ConnectionString = "Data Source=************,1437;Initial Catalog=TORRE_SAP;User ID=svc_idm;Password=***********;Pooling=False"
	dbLineProvider.TimeZone = TimeZoneInfo.FindSystemTimeZoneById("E. South America Standard Time")

	' SQL statement
	Dim statement As New PlainSqlStatement()
	statement.Statement = "SELECT * FROM ( " & _
"SELECT personnelnumber, firstname, lastname, salutation, title, preferredname, personaltitle, gender, birthdate, isinactive, uid_department, uid_profitcenter, entrydate, exitdate, companymember, uid_personhead, uid_locality, uid_dialogcountry, '' as phone, phonemobile, defaultemailaddress, customproperty01, customproperty02, customproperty03, customproperty04, customproperty05, customproperty06, customproperty07, TIMESTAMP, " & _
"ROW_NUMBER() OVER (PARTITION BY customproperty02 ORDER BY personnelnumber DESC) col  " & _
"FROM Person where Timestamp > Dateadd(hh," & Connection.GetConfigParm("Custom\DB-RH\TimeStampPerson") & ", getdate()) and customproperty02 <> '') x " & _
"WHERE x.col = 1 and isinactive <> '6'"
	
	dbLineProvider.Statement = statement
	lineProvider = dbLineProvider

	'
	' Configure column resolution
	'

	' Column indices
	Const iBirthDate = 8
	Const iCompanyMember = 21
	Const iCustomProperty02 = 22
	Const iCustomProperty03 = 23
	Const iCustomProperty04 = 24
	Const iCustomProperty05 = 25
	Const iCustomProperty06 = 26
	Const iCustomProperty07 = 27
	Const iCustomProperty08 = 28
	Const iEntryDate = 12
	Const iFirstName = 1
	Const iGender = 7
	Const iLastName = 2
	Const iPersonalTitle = 6
	Const iPersonnelNumber = 0
	Const iPhone = 18
	Const iPhoneMobile = 19
	Const iPreferredName = 5
	Const iSalutation = 3
	Const iTitle = 4
	Const iUID_Department = 10
	Const iUID_Locality = 16
	Const iUID_ProfitCenter = 11

	' Resolver to get column data from import data
	Dim columnBirthDate As IResolveImportValue = New ResolveImportValueSimple("BirthDate")
	Dim columnCompanyMember As IResolveImportValue = New ResolveImportValueSimple("CompanyMember")
	Dim columnCustomProperty02 As IResolveImportValue = New ResolveImportValueSimple("CustomProperty02")
	Dim columnCustomProperty03 As IResolveImportValue = New ResolveImportValueSimple("CustomProperty03")
	Dim columnCustomProperty04 As IResolveImportValue = New ResolveImportValueSimple("CustomProperty04")
	Dim columnCustomProperty05 As IResolveImportValue = New ResolveImportValueSimple("CustomProperty05")
	Dim columnCustomProperty06 As IResolveImportValue = New ResolveImportValueSimple("CustomProperty06")
	Dim columnCustomProperty07 As IResolveImportValue = New ResolveImportValueSimple("CustomProperty07")
	Dim columnCustomProperty08 As IResolveImportValue = New ResolveImportValueSimple("CustomProperty08")
	Dim columnEntryDate As IResolveImportValue = New ResolveImportValueSimple("EntryDate")
	Dim columnFirstName As IResolveImportValue = New ResolveImportValueSimple("FirstName")
	Dim columnGender As IResolveImportValue = New ResolveImportValueSimple("Gender")
	Dim columnLastName As IResolveImportValue = New ResolveImportValueSimple("LastName")
	Dim columnPersonalTitle As IResolveImportValue = New ResolveImportValueSimple("PersonalTitle")
	Dim columnPersonnelNumber As IResolveImportValue = New ResolveImportValueSimple("PersonnelNumber")
	Dim columnPhone As IResolveImportValue = New ResolveImportValueSimple("Phone")
	Dim columnPhoneMobile As IResolveImportValue = New ResolveImportValueSimple("PhoneMobile")
	Dim columnPreferredName As IResolveImportValue = New ResolveImportValueSimple("PreferredName")
	Dim columnSalutation As IResolveImportValue = New ResolveImportValueSimple("Salutation")
	Dim columnTitle As IResolveImportValue = New ResolveImportValueSimple("Title")

	' Build a dictionary: The last parameter forces exceptions when duplicate keys are found
	Dim columnUID_Department As New ResolveImportValueHashed( _
		Connection, _
		ObjectWalker.ColDefs(table, "FK(UID_Department).DepartmentName"), False)

	' Build a dictionary: The last parameter forces exceptions when duplicate keys are found
	Dim columnUID_Locality As New ResolveImportValueHashed( _
		Connection, _
		ObjectWalker.ColDefs(table, "FK(UID_Locality).Ident_locality"), False)

	' Build a dictionary: The last parameter forces exceptions when duplicate keys are found
	Dim columnUID_ProfitCenter As New ResolveImportValueHashed( _
		Connection, _
		ObjectWalker.ColDefs(table, "FK(UID_ProfitCenter).AccountNumber"), False)

	Try

		'
		' Create a dictionary for the destination table
		'
		Dim colImport As IEntityCollection
		Dim elemsByKey As New Dictionary(Of String, IEntity)(StringComparer.OrdinalIgnoreCase)
		Dim imported As New HashSet(Of DbObjectKey)()

		logSection = dfImport.LogSection(#LD("Indexing existing data...")#)
		Try
			Dim query As Query = Query.From("Person").SelectNone()

			' Set keys and values as display items to load them with the collection
			query = query.Select("BirthDate")
			query = query.Select("CompanyMember")
			query = query.Select("CustomProperty01")
			query = query.Select("CustomProperty02")
			query = query.Select("CustomProperty03")
			query = query.Select("CustomProperty04")
			query = query.Select("CustomProperty05")
			query = query.Select("CustomProperty06")
			query = query.Select("CustomProperty07")
			query = query.Select("CustomProperty08")
			query = query.Select("EntryDate")
			query = query.Select("FirstName")
			query = query.Select("Gender")
			query = query.Select("LastName")
			query = query.Select("PersonalTitle")
			query = query.Select("PersonnelNumber")
			query = query.Select("Phone")
			query = query.Select("PhoneMobile")
			query = query.Select("PreferredName")
			query = query.Select("Salutation")
			query = query.Select("Title")
			query = query.Select("UID_Department")
			query = query.Select("UID_Locality")
			query = query.Select("UID_ProfitCenter")
			query = query.Select("xmarkedfordeletion")

			' Set WHERE clause
			query = query.Where("IsExternal = 0").GetQuery()

			' Load the collection
			colImport = Session.Source().GetCollection(query, EntityCollectionLoadType.Slim)

			' Put the entries in the dictionary
			For Each elem In colImport
				Dim key As String

				key = elem.GetValue("CustomProperty02").String

				If elemsByKey.ContainsKey(key) Then
					dfImport.Log(MsgSeverity.Serious, #LD("Duplicate key: {0}", key)#)
				Else
					elemsByKey.Add(key, elem)
				End If
			Next
		Finally
			If Not logSection Is Nothing Then
				logSection.Dispose()
				logSection = Nothing
			End If
		End Try

		logSection = dfImport.LogSection(#LD("Import data from file")#)
		Try
			For Each line As Line In lineProvider.GetLines(counter)

				' Pay attention to the Stop flag
				If StopProcessing Then
					dfImport.Log(MsgSeverity.Warning, #LD("Import was stopped.")#)
					Exit For
				End If

				Try
					' Get raw data from the current line
					Dim key As String


					Dim valBirthDate As String
					Dim resBirthDate As DateTime

					Dim valCompanyMember As String
					Dim resCompanyMember As String
					'Custom add 08/22/2017
					Dim valCustomProperty01 As String
					Dim resCustomProperty01 As String
					
					Dim valCustomProperty02 As String
					Dim resCustomProperty02 As String

					Dim valCustomProperty03 As String
					Dim resCustomProperty03 As String

					Dim valCustomProperty04 As String
					Dim resCustomProperty04 As String

					Dim valCustomProperty05 As String
					Dim resCustomProperty05 As String

					Dim valCustomProperty06 As String
					Dim resCustomProperty06 As String

					Dim valCustomProperty07 As String
					Dim resCustomProperty07 As String

					Dim valCustomProperty08 As String
					Dim resCustomProperty08 As String

					Dim valEntryDate As String
					Dim resEntryDate As DateTime

					Dim valFirstName As String
					Dim resFirstName As String

					Dim valGender As String
					Dim resGender As Int32

					Dim valLastName As String
					Dim resLastName As String

					Dim valPersonalTitle As String
					Dim resPersonalTitle As String

					Dim valPersonnelNumber As String
					Dim resPersonnelNumber As String

					Dim valPhone As String
					Dim resPhone As String

					Dim valPhoneMobile As String
					Dim resPhoneMobile As String

					Dim valPreferredName As String
					Dim resPreferredName As String

					Dim valSalutation As String
					Dim resSalutation As String

					Dim valTitle As String
					Dim resTitle As String

					Dim valUID_Department As String
					Dim resUID_Department As String

					Dim valUID_Locality As String
					Dim resUID_Locality As String

					Dim valUID_ProfitCenter As String
					Dim resUID_ProfitCenter As String
						'custom add 08/22/2017
						valCustomProperty01 = "RH"
						valBirthDate = line.GetValue(iBirthDate)
						valCompanyMember = line.GetValue(iCompanyMember)
						valCustomProperty02 = line.GetValue(iCustomProperty02)
						valCustomProperty03 = line.GetValue(iCustomProperty03)
						valCustomProperty04 = line.GetValue(iCustomProperty04)
						valCustomProperty05 = line.GetValue(iCustomProperty05)
						valCustomProperty06 = line.GetValue(iCustomProperty06)
						valCustomProperty07 = line.GetValue(iCustomProperty07)
						valCustomProperty08 = line.GetValue(iCustomProperty08)
						valEntryDate = line.GetValue(iEntryDate)
						valFirstName = line.GetValue(iFirstName)
						valGender = line.GetValue(iGender)
						valLastName = line.GetValue(iLastName)
						valPersonalTitle = line.GetValue(iPersonalTitle)
						valPersonnelNumber = line.GetValue(iPersonnelNumber)
						valPhone = line.GetValue(iPhone)
						valPhoneMobile = line.GetValue(iPhoneMobile)
						valPreferredName = line.GetValue(iPreferredName)
						valSalutation = line.GetValue(iSalutation)
						valTitle = line.GetValue(iTitle)
						valUID_Department = line.GetValue(iUID_Department)
						valUID_Locality = line.GetValue(iUID_Locality)
						valUID_ProfitCenter = line.GetValue(iUID_ProfitCenter)

						'
						' Here is the place to check and change raw data
						'
						lineData.Line = line

						Value = valCustomProperty02

						' Start of convert script for CustomProperty02
						value = GetValue("22").String.Replace(".","").Replace("-","")
						' End of convert script for CustomProperty02

						valCustomProperty02 = DbVal.ConvertTo(Of String)(Value)

						Value = valSalutation

						' Start of convert script for Salutation
						If GetValue("3").String = "2" Then
value = "Ms"
Else
value = "Mr"
End If
						' End of convert script for Salutation

						valSalutation = DbVal.ConvertTo(Of String)(Value)


						' Convert raw data to final column data values
						resBirthDate = DbVal.ConvertTo(Of DateTime)(columnBirthDate.ResolveValue(valBirthDate), lineProvider.Culture)
						resBirthDate = DbVal.ToUniversalTime(resBirthDate, lineProvider.TimeZone)
						resCompanyMember = DbVal.ConvertTo(Of String)(columnCompanyMember.ResolveValue(valCompanyMember), lineProvider.Culture)
						'Custom add 08/22/2017
						resCustomProperty01 = valCustomProperty01
						resCustomProperty02 = DbVal.ConvertTo(Of String)(columnCustomProperty02.ResolveValue(valCustomProperty02), lineProvider.Culture)
						resCustomProperty03 = DbVal.ConvertTo(Of String)(columnCustomProperty03.ResolveValue(valCustomProperty03), lineProvider.Culture)
						resCustomProperty04 = DbVal.ConvertTo(Of String)(columnCustomProperty04.ResolveValue(valCustomProperty04), lineProvider.Culture)
						resCustomProperty05 = DbVal.ConvertTo(Of String)(columnCustomProperty05.ResolveValue(valCustomProperty05), lineProvider.Culture)
						resCustomProperty06 = DbVal.ConvertTo(Of String)(columnCustomProperty06.ResolveValue(valCustomProperty06), lineProvider.Culture)
						resCustomProperty07 = DbVal.ConvertTo(Of String)(columnCustomProperty07.ResolveValue(valCustomProperty07), lineProvider.Culture)
						resCustomProperty08 = DbVal.ConvertTo(Of String)(columnCustomProperty08.ResolveValue(valCustomProperty08), lineProvider.Culture)
						resEntryDate = DbVal.ConvertTo(Of DateTime)(columnEntryDate.ResolveValue(valEntryDate), lineProvider.Culture)
						resEntryDate = DbVal.ToUniversalTime(resEntryDate, lineProvider.TimeZone)
						resFirstName = DbVal.ConvertTo(Of String)(columnFirstName.ResolveValue(valFirstName), lineProvider.Culture)
						resGender = DbVal.ConvertTo(Of Int32)(columnGender.ResolveValue(valGender), lineProvider.Culture)
						resLastName = DbVal.ConvertTo(Of String)(columnLastName.ResolveValue(valLastName), lineProvider.Culture)
						resPersonalTitle = DbVal.ConvertTo(Of String)(columnPersonalTitle.ResolveValue(valPersonalTitle), lineProvider.Culture)
						resPersonnelNumber = DbVal.ConvertTo(Of String)(columnPersonnelNumber.ResolveValue(valPersonnelNumber), lineProvider.Culture)
						resPhone = DbVal.ConvertTo(Of String)(columnPhone.ResolveValue(valPhone), lineProvider.Culture)
						resPhoneMobile = DbVal.ConvertTo(Of String)(columnPhoneMobile.ResolveValue(valPhoneMobile), lineProvider.Culture)
						resPreferredName = DbVal.ConvertTo(Of String)(columnPreferredName.ResolveValue(valPreferredName), lineProvider.Culture)
						resSalutation = DbVal.ConvertTo(Of String)(columnSalutation.ResolveValue(valSalutation), lineProvider.Culture)
						resTitle = DbVal.ConvertTo(Of String)(columnTitle.ResolveValue(valTitle), lineProvider.Culture)
						resUID_Department = DbVal.ConvertTo(Of String)(columnUID_Department.ResolveValue(valUID_Department), lineProvider.Culture)
						resUID_Locality = DbVal.ConvertTo(Of String)(columnUID_Locality.ResolveValue(valUID_Locality), lineProvider.Culture)
						resUID_ProfitCenter = DbVal.ConvertTo(Of String)(columnUID_ProfitCenter.ResolveValue(valUID_ProfitCenter), lineProvider.Culture)


						'
						' Here is the place to check and change final column data
						'

						'
						' Here starts the standard handling. It is recommended to not change anything beyond this point.
						'

						'
						' Create key for this line
						'
						key = DbVal.ConvertTo(Of String)(resCustomProperty02)
					Dim elem As IEntity = Nothing
					Dim entity As IEntity = Nothing

					If elemsByKey.TryGetValue(key, elem) Then
						'
						' Found -> Update
						'

						' Mark element for superset handling
						imported.Add(New DbObjectKey(table, elem))


						' Map values
						Dim fullEntity As New Lazy(Of IEntity)(Function() elem.Create(Session))

						'custom change 04/07/2017
						If DbVal.Compare(elem.GetRaw("CustomProperty01"), resCustomProperty01, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("CustomProperty01", resCustomProperty01)
						End If
						
						If DbVal.Compare(elem.GetRaw("BirthDate"), resBirthDate, ValType.Date) <> 0 Then
							fullEntity.Value.PutValue("BirthDate", resBirthDate)
						End If

						If DbVal.Compare(elem.GetRaw("CompanyMember"), resCompanyMember, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("CompanyMember", resCompanyMember)
						End If

						If DbVal.Compare(elem.GetRaw("CustomProperty03"), resCustomProperty03, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("CustomProperty03", resCustomProperty03)
						End If

						If DbVal.Compare(elem.GetRaw("CustomProperty04"), resCustomProperty04, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("CustomProperty04", resCustomProperty04)
						End If

						If DbVal.Compare(elem.GetRaw("CustomProperty05"), resCustomProperty05, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("CustomProperty05", resCustomProperty05)
						End If

						If DbVal.Compare(elem.GetRaw("CustomProperty06"), resCustomProperty06, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("CustomProperty06", resCustomProperty06)
						End If

						If DbVal.Compare(elem.GetRaw("CustomProperty07"), resCustomProperty07, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("CustomProperty07", resCustomProperty07)
						End If

						If DbVal.Compare(elem.GetRaw("CustomProperty08"), resCustomProperty08, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("CustomProperty08", resCustomProperty08)
						End If

						If DbVal.Compare(elem.GetRaw("EntryDate"), resEntryDate, ValType.Date) <> 0 Then
							fullEntity.Value.PutValue("EntryDate", resEntryDate)
						End If

						If DbVal.Compare(elem.GetRaw("FirstName"), resFirstName, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("FirstName", resFirstName)
						End If

						If DbVal.Compare(elem.GetRaw("Gender"), resGender, ValType.Int) <> 0 Then
							fullEntity.Value.PutValue("Gender", resGender)
						End If

						If DbVal.Compare(elem.GetRaw("LastName"), resLastName, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("LastName", resLastName)
						End If

						If DbVal.Compare(elem.GetRaw("PersonalTitle"), resPersonalTitle, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("PersonalTitle", resPersonalTitle)
						End If

						If DbVal.Compare(elem.GetRaw("PersonnelNumber"), resPersonnelNumber, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("PersonnelNumber", resPersonnelNumber)
						End If

						If DbVal.Compare(elem.GetRaw("Phone"), resPhone, ValType.Text) <> 0 Then
							fullEntity.Value.PutValue("Phone", resPhone)
						End If

						If DbVal.Compare(elem.GetRaw("PhoneMobile"), resPhoneMobile, ValType.Text) <> 0 Then
							fullEntity.Value.PutValue("PhoneMobile", resPhoneMobile)
						End If

						If DbVal.Compare(elem.GetRaw("PreferredName"), resPreferredName, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("PreferredName", resPreferredName)
						End If

						If DbVal.Compare(elem.GetRaw("Salutation"), resSalutation, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("Salutation", resSalutation)
						End If

						If DbVal.Compare(elem.GetRaw("Title"), resTitle, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("Title", resTitle)
						End If

						If DbVal.Compare(elem.GetRaw("UID_Department"), resUID_Department, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("UID_Department", resUID_Department)
						End If

						If DbVal.Compare(elem.GetRaw("UID_Locality"), resUID_Locality, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("UID_Locality", resUID_Locality)
						End If

						If DbVal.Compare(elem.GetRaw("UID_ProfitCenter"), resUID_ProfitCenter, ValType.String) <> 0 Then
							fullEntity.Value.PutValue("UID_ProfitCenter", resUID_ProfitCenter)
						End If

						If fullEntity.IsValueCreated Or elem.IsDeleted() Then

							entity = fullEntity.Value
						Else
							counter.Increment(LineType.Unchanged)
						End If

					Else
						'
						' Not found -> Insert
						'

						entity = Session.Source().CreateNew("Person")

						' Fill keys and values
						entity.PutValue("CustomProperty01", resCustomProperty01) 'custom add 08/22/2017
						entity.PutValue("BirthDate", resBirthDate)
						entity.PutValue("CompanyMember", resCompanyMember)
						entity.PutValue("CustomProperty02", resCustomProperty02)
						entity.PutValue("CustomProperty03", resCustomProperty03)
						entity.PutValue("CustomProperty04", resCustomProperty04)
						entity.PutValue("CustomProperty05", resCustomProperty05)
						entity.PutValue("CustomProperty06", resCustomProperty06)
						entity.PutValue("CustomProperty07", resCustomProperty07)
						entity.PutValue("CustomProperty08", resCustomProperty08)
						entity.PutValue("EntryDate", resEntryDate)
						entity.PutValue("FirstName", resFirstName)
						entity.PutValue("Gender", resGender)
						entity.PutValue("LastName", resLastName)
						entity.PutValue("PersonalTitle", resPersonalTitle)
						entity.PutValue("PersonnelNumber", resPersonnelNumber)
						entity.PutValue("Phone", resPhone)
						entity.PutValue("PhoneMobile", resPhoneMobile)
						entity.PutValue("PreferredName", resPreferredName)
						entity.PutValue("Salutation", resSalutation)
						entity.PutValue("Title", resTitle)
						entity.PutValue("UID_Department", resUID_Department)
						entity.PutValue("UID_Locality", resUID_Locality)
						entity.PutValue("UID_ProfitCenter", resUID_ProfitCenter)

					End If

					'
					' Additional values can be put into the entity here
					'

					If Not entity Is Nothing Then

						If entity.IsDeleted() Then
							' Recall deleted entity
							entity.MarkForRecall()
						End If

						Dim isUpdate As Boolean = entity.IsLoaded

						' Save it
						If entity.IsDifferent Then
							entity.Save(Session)

							If isUpdate Then
								counter.Increment(LineType.Updated)
							Else
								counter.Increment(LineType.Inserted)
							End If
						Else
							counter.Increment(LineType.Unchanged)
						End If

						If elem Is Nothing Then
							' Include in our collection to avoid double imports of elements

							Dim keyInsert As String
							keyInsert = entity.GetValue("CustomProperty02").String

							elemsByKey(keyInsert) = entity
							imported.Add(New DbObjectKey(table, entity))
						End If
					End If

				Catch ex As Exception
					dfImport.Log(MsgSeverity.Serious, #LD("Error in line {0}: {1}", line.OriginalLineNumber, ViException.ErrorString(ex))#)
					dfImport.Log(MsgSeverity.Serious, #LD("Line data was: {0}", line.ToString())#)

					counter.Increment(LineType.Error)
				End Try

				If (counter(LineType.Total) Mod 100) = 0 Then
					dfImport.SetProgressInfo(#LD("{0} lines imported, {1} ignored, {2} inserted, {3} updated, {4} errors", counter(LineType.Total), counter(LineType.Unchanged), counter(LineType.Inserted), counter(LineType.Updated), counter(LineType.Error))#)
				End If

			Next ' ForEach line

		Finally
			If Not logSection Is Nothing Then
				logSection.Dispose()
				logSection = Nothing
			End If
		End Try

		'
		' No superset handling
		'
	Finally

	End Try

	Using dfImport.LogSection(#LD("Results")#)
		dfImport.Log(#LD("{0} lines imported", counter(LineType.Total))#)
		dfImport.Log(#LD("{0} header lines", counter(LineType.Header))#)
		dfImport.Log(#LD("{0} inserted", counter(LineType.Inserted))#)
		dfImport.Log(#LD("{0} changed", counter(LineType.Updated))#)
		dfImport.Log(#LD("{0} deleted", counter(LineType.Deleted))#)
		dfImport.Log(#LD("{0} not changed", counter(LineType.Unchanged))#)
		dfImport.Log(#LD("{0} not found", counter(LineType.NotFound))#)
		dfImport.Log(#LD("{0} empty lines", counter(LineType.Empty))#)
		dfImport.Log(#LD("{0} errors", counter(LineType.Error))#)
	End Using
End Sub