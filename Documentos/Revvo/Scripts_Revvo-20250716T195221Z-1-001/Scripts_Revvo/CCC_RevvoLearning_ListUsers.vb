#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
#End If

' Função de autenticação embarcada
Private Function CCC_RevvoLearning_Auth(ByVal clientId As String, ByVal clientSecret As String) As String
    Dim accessToken As String = Nothing
    
    Try
        Using client As New WebClient()
            client.Headers(HttpRequestHeader.ContentType) = "application/json"
            
            Dim jsonData As String = String.Format(
                "{{""grant_type"": ""client_credentials"", ""client_id"": ""{0}"", ""client_secret"": ""{1}""}}",
                clientId, clientSecret
            )
            
            Dim requestBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(jsonData)
            Dim responseBytes As Byte() = client.UploadData("https://dasa-poc.learningflix.net/webservice/api/oauth2/token", "POST", requestBytes)
            Dim responseString As String = System.Text.Encoding.UTF8.GetString(responseBytes)
            
            Dim jsonResponse As JObject = JObject.Parse(responseString)
            
            If jsonResponse IsNot Nothing AndAlso jsonResponse("access_token") IsNot Nothing Then
                accessToken = jsonResponse("access_token").ToString()
                Console.WriteLine("Token de acesso obtido com sucesso para RevvoLearning.")
            Else
                Console.WriteLine("Resposta JSON não contém 'access_token' ou está vazia.")
            End If
            
        End Using
        
    Catch ex As WebException
        Console.WriteLine("Erro na requisição web durante a autenticação RevvoLearning: " & ex.Message)
        Return Nothing
    Catch ex As Exception
        Console.WriteLine("Ocorreu um erro inesperado durante a autenticação RevvoLearning: " & ex.Message)
        Return Nothing
    End Try
    
    Return accessToken
End Function

Public Function CCC_RevvoLearning_ListUsers(ByVal clientId As String, ByVal clientSecret As String, Optional ByVal page As Integer = 1, Optional ByVal perpage As Integer = 100) As String
    Dim authToken As String = CCC_RevvoLearning_Auth(clientId, clientSecret)
    
    If String.IsNullOrEmpty(authToken) Then
        Return ""
    End If
    
    Dim apiUrl As String = String.Format("https://dasa-poc.learningflix.net/webservice/api/v3/users?page={0}&perpage={1}", page, perpage)
    
    Try
        Using client As New WebClient()
            client.Headers.Add("Authorization", "Bearer " & authToken)
            
            Dim responseString As String = client.DownloadString(apiUrl)
            Return responseString
        End Using
        
    Catch ex As Exception
        Return ""
    End Try
End Function

' --- Exemplo de como usaria esta função em outro script ---
' Sub Main() ' Ou o contexto onde você chamaria esta função no One Identity Manager
'     ' Exemplo 1: Listar primeiros 10 usuários
'     Dim usersJson As String = CCC_RevvoLearning_ListUsers(1, 10)
'     
'     If Not String.IsNullOrEmpty(usersJson) Then
'         Console.WriteLine("Usuários obtidos com sucesso!")
'         Console.WriteLine("JSON Response: " & usersJson)
'         
'         ' Parse para processar os dados
'         Dim jsonResponse As JObject = JObject.Parse(usersJson)
'         Dim users As JArray = jsonResponse("users")
'         
'         For Each user As JObject In users
'             Console.WriteLine("- " & user("username").ToString() & " (" & user("email").ToString() & ")")
'         Next
'     Else
'         Console.WriteLine("Falha ao obter lista de usuários.")
'     End If
'     
'     ' Exemplo 2: Paginar através de todos os usuários
'     Dim currentPage As Integer = 1
'     Dim hasMorePages As Boolean = True
'     
'     While hasMorePages
'         Dim pageResult As String = CCC_RevvoLearning_ListUsers(currentPage, 50)
'         
'         If Not String.IsNullOrEmpty(pageResult) Then
'             Dim pageJson As JObject = JObject.Parse(pageResult)
'             Dim pageUsers As JArray = pageJson("users")
'             Dim totalUsers As Integer = pageJson("total").ToObject(Of Integer)()
'             
'             Console.WriteLine("Página " & currentPage & ": " & pageUsers.Count & " usuários de " & totalUsers & " total")
'             
'             ' Verifica se há mais páginas
'             If (currentPage * 50) >= totalUsers Then
'                 hasMorePages = False
'             Else
'                 currentPage += 1
'             End If
'         Else
'             hasMorePages = False
'         End If
'     End While
' End Sub



