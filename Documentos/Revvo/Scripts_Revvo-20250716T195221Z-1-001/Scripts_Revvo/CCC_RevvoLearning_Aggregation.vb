#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
Imports System.Text.RegularExpressions
#End If

Public Sub CCC_RevvoLearning_Aggregation(ByVal clientId As String, ByVal clientSecret As String, Optional ByVal pageSize As Integer = 50)
    
    Dim accessToken As String = CCC_RevvoLearning_Auth(clientId, clientSecret)
    
    If String.IsNullOrEmpty(accessToken) Then
        Return
    End If

    Dim userApiBaseUrl As String = "https://dasa-poc.learningflix.net/webservice/api/v3/users"
    Dim hasNextPage As Boolean = True
    Dim nextPageUrl As String = userApiBaseUrl & "?_limit=" & pageSize.ToString()

    While hasNextPage
        Dim apiResponseString As String = String.Empty
        
        Try
            Using client As New WebClient()
                client.Headers(HttpRequestHeader.Authorization) = "Bearer " & accessToken
                client.Headers(HttpRequestHeader.ContentType) = "application/json"
                apiResponseString = client.DownloadString(nextPageUrl)
            End Using
        Catch ex As Exception
            Exit While
        End Try

        Try
            Dim jsonResponse As JObject = JObject.Parse(apiResponseString)
            Dim embeddedUsers As JArray = Nothing
            
            If jsonResponse("_embedded") IsNot Nothing AndAlso jsonResponse("_embedded")("users") IsNot Nothing Then
                embeddedUsers = CType(jsonResponse("_embedded")("users"), JArray)
            End If

            If embeddedUsers IsNot Nothing AndAlso embeddedUsers.HasValues Then
                Dim userIndex As Integer = 0
                Do While userIndex < embeddedUsers.Count
                    Dim userJson As JObject = CType(embeddedUsers(userIndex), JObject)
                    
                    Dim usernameApi As String = ""
                    If userJson("username") IsNot Nothing Then
                        usernameApi = userJson("username").ToString()
                    End If

                    If Not String.IsNullOrEmpty(usernameApi) Then
                        Try
                            Dim f = Session.SqlFormatter
                            Dim personManagerAdsAccountQuery = f.UidComparison("Ident_Main", usernameApi)
                            Dim personManagerAdsAccountCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(personManagerAdsAccountQuery).SelectNonLobs)

                            Dim unsAccount As IEntity
                            Dim isNewAccount As Boolean = False

                            If personManagerAdsAccountCol.Count = 0 Then
                                unsAccount = Session.Source().CreateNew("UNSAccountB")
                                isNewAccount = True
                            Else
                                unsAccount = personManagerAdsAccountCol(0)
                            End If

                            Dim mappingIndex As Integer = 0
                            Dim mappingKeys() As String = {"username", "firstname", "lastname", "email", "id", "city", "phone1"}
                            Dim mappingValues() As String = {"Ident_Main", "FirstName", "LastName", "Ident_PersonalEmail", "UID_ExternalUser", "City", "Phone"}
                            
                            Do While mappingIndex < mappingKeys.Length
                                Dim apiFieldName As String = mappingKeys(mappingIndex)
                                Dim oimFieldName As String = mappingValues(mappingIndex)
                                
                                If userJson(apiFieldName) IsNot Nothing Then
                                    Dim apiValue As String = userJson(apiFieldName).ToString()
                                    
                                    Select Case oimFieldName
                                        Case "Ident_Main"
                                            apiValue = Regex.Replace(apiValue, "[^a-zA-Z0-9\._-]", "").ToLower()
                                        Case "Ident_PersonalEmail"
                                            If Not Regex.IsMatch(apiValue, "^[^@\s]+@[^@\s]+\.[^@\s]+$") Then
                                                mappingIndex += 1
                                                Continue Do
                                            End If
                                    End Select
                                    
                                    unsAccount.PutValue(oimFieldName, apiValue)
                                End If
                                
                                mappingIndex += 1
                            Loop

                            If isNewAccount Then
                                unsAccount.PutValue("CentralAccount", True)
                                unsAccount.PutValue("Ident_Domain", "RevvoLearning")
                                
                                Dim firstName As String = ""
                                Dim lastName As String = ""
                                If userJson("firstname") IsNot Nothing Then firstName = userJson("firstname").ToString()
                                If userJson("lastname") IsNot Nothing Then lastName = userJson("lastname").ToString()
                                
                                unsAccount.PutValue("DisplayName", firstName & " " & lastName)
                            End If

                            unsAccount.Save(Session)

                        Catch ex As Exception
                            ' Continue para próximo usuário
                        End Try
                    End If
                    
                    userIndex += 1
                Loop

                Dim nextLink As JObject = Nothing
                If jsonResponse("_links") IsNot Nothing AndAlso jsonResponse("_links")("next") IsNot Nothing Then
                    nextLink = CType(jsonResponse("_links")("next"), JObject)
                End If

                If nextLink IsNot Nothing AndAlso nextLink("href") IsNot Nothing Then
                    nextPageUrl = nextLink("href").ToString()
                    hasNextPage = True
                Else
                    hasNextPage = False
                End If
            Else
                hasNextPage = False
            End If
        Catch ex As Exception
            hasNextPage = False
        End Try
    End While

End Sub

' --- Como você chamaria este script no One Identity Manager ---
' No seu workflow, processo de sincronização ou job agendado:

' Dim clientId As String = DprSystem.GetConfigParam("CCC_RevvoLearning_ClientId").String
' Dim clientSecret As String = DprSystem.GetConfigParam("CCC_RevvoLearning_ClientSecret").String
' Dim currentConnection As IConnection = Connection

' Dim authModule As New AuthModule() ' Supondo que sua função de autenticação esteja em um módulo AuthModule
' Dim aggregateScript As New UserAggregationScript(AddressOf authModule.CCC_RevvoLearning_Auth)

' aggregateScript.AggregateUsers(myClientId, myClientSecret, currentConnection)

' Ou se a função de autenticação estiver no mesmo script (menos modular, mas possível):
' Dim aggregateScript As New UserAggregationScript(AddressOf Me.CCC_RevvoLearning_Auth)
' aggregateScript.AggregateUsers(myClientId, myClientSecret, Connection)

' Lembre-se que "Connection" geralmente é um objeto disponível globalmente nos scripts do OIM.
' Para testar fora do OIM, você precisaria criar uma conexão IConnection real com seu banco de dados do OIM.














