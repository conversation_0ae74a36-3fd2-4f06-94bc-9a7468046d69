#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
#End If

Public Function CCC_RevvoLearning_CreateUser(ByVal clientId As String, ByVal clientSecret As String, ByVal UID_UNSAccountB As String) As String
    Dim logMessage As String = ""

    Try
        logMessage = String.Format("[{0}] Iniciando criação de usuário para UID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), UID_UNSAccountB)
        Console.WriteLine(logMessage)

        Dim f = Session.SqlFormatter
        Dim queryUNSAccountB = f.UidComparison("UID_UNSAccountB", UID_UNSAccountB)
        Dim UNSAccountBCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(queryUNSAccountB).SelectNonLobs)

    If UNSAccountBCol.Count > 0 Then
        Dim UNSAccountB As IEntity = UNSAccountBCol(0)

        Dim username As String = UNSAccountB.GetValue("CCC_CustomProperty20").String
        Dim firstname As String = UNSAccountB.GetValue("FirstName").String
        Dim lastname As String = UNSAccountB.GetValue("LastName").String
        Dim email As String = UNSAccountB.GetValue("CCC_CustomProperty15").String
        Dim password As String = ""

        Dim authToken As String = CCC_RevvoLearning_Auth(clientId, clientSecret)

        If String.IsNullOrEmpty(authToken) Then
            logMessage = String.Format("[{0}] ERRO: Falha na autenticação - Token não obtido", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)
            Return "ERRO: Falha na autenticação"
        End If

        logMessage = String.Format("[{0}] Token obtido com sucesso", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
        Console.WriteLine(logMessage)
        
        Dim jsonData As String = String.Format(
            "{{""user"": {{""username"": ""{0}"", ""firstname"": ""{1}"", ""lastname"": ""{2}"", ""email"": ""{3}"", ""password"": """", ""createpassword"": true}}}}",
            username, firstname, lastname, email
        )

        logMessage = String.Format("[{0}] Enviando requisição para criar usuário na API RevvoLearning...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
        Console.WriteLine(logMessage)

        Try
            Using client As New WebClient()
                client.Headers.Add("Content-Type", "application/json")
                client.Headers.Add("Authorization", "Bearer " & authToken)

                Dim responseBytes As Byte() = client.UploadData("https://dasa-poc.learningflix.net/webservice/api/v3/users", "POST", System.Text.Encoding.ASCII.GetBytes(jsonData))
                Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)

                logMessage = String.Format("[{0}] Resposta da API recebida: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), responseString)
                Console.WriteLine(logMessage)

                Dim jsonResponse As JObject = JObject.Parse(responseString)
                Dim userId As String = jsonResponse("id").ToString()

                logMessage = String.Format("[{0}] Usuário criado com sucesso! ID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), userId)
                Console.WriteLine(logMessage)

                UNSAccountB.PutValue("CCC_CustomProperty24", userId)
                UNSAccountB.Save(Session)

                logMessage = String.Format("[{0}] ID do usuário salvo no campo CCC_CustomProperty24", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                Console.WriteLine(logMessage)

                Return "SUCESSO: Usuário criado com ID " & userId

            End Using
        Catch webEx As WebException
            Dim errorResponse As String = ""
            If webEx.Response IsNot Nothing Then
                Using reader As New StreamReader(webEx.Response.GetResponseStream())
                    errorResponse = reader.ReadToEnd()
                End Using
            End If

            logMessage = String.Format("[{0}] ERRO na API: {1} - Resposta: {2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), webEx.Message, errorResponse)
            Console.WriteLine(logMessage)
            Return "ERRO: Falha na API - " & webEx.Message & " | Resposta: " & errorResponse

        Catch ex As Exception
            logMessage = String.Format("[{0}] ERRO geral: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), ex.Message)
            Console.WriteLine(logMessage)
            Return "ERRO: " & ex.Message
        End Try

    Else
        logMessage = String.Format("[{0}] ERRO: Conta não encontrada para UID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), UID_UNSAccountB)
        Console.WriteLine(logMessage)
        Return "ERRO: Conta não encontrada"
    End If

    Catch generalEx As Exception
        logMessage = String.Format("[{0}] ERRO CRÍTICO: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), generalEx.Message)
        Console.WriteLine(logMessage)
        Return "ERRO CRÍTICO: " & generalEx.Message
    End Try
End Function

' --- Exemplo de como usaria esta função em outro script ---
' Sub Main() ' Ou o contexto onde você chamaria esta função no One Identity Manager
'     ' Configurações da API
'     Dim clientId As String = "seu_client_id"
'     Dim clientSecret As String = "seu_client_secret"
'
'     ' Exemplo 1: Criar usuário para conta específica
'     Dim uidAccount As String = "UID-12345-ABCDE"
'
'     Dim resultado As String = CCC_RevvoLearning_CreateUser(clientId, clientSecret, uidAccount)
'     Console.WriteLine("Resultado: " & resultado)
'
'     ' Verificar se foi bem-sucedido
'     If resultado.StartsWith("SUCESSO") Then
'         Console.WriteLine("✓ Usuário criado/atualizado com sucesso!")
'     Else
'         Console.WriteLine("✗ Falha na criação do usuário: " & resultado)
'     End If
'
'     ' Exemplo 2: Criar usuários em lote (para sincronização inicial)
'     Dim accountUIDs As String() = {"UID-001", "UID-002", "UID-003"}
'     Dim sucessos As Integer = 0
'     Dim falhas As Integer = 0
'
'     Console.WriteLine("=== INICIANDO CRIAÇÃO EM LOTE ===")
'     For Each uid As String In accountUIDs
'         Console.WriteLine(String.Format("Processando {0} de {1}: {2}", Array.IndexOf(accountUIDs, uid) + 1, accountUIDs.Length, uid))
'
'         Dim resultado As String = CCC_RevvoLearning_CreateUser(clientId, clientSecret, uid)
'
'         If resultado.StartsWith("SUCESSO") Then
'             sucessos += 1
'             Console.WriteLine("✓ " & resultado)
'         Else
'             falhas += 1
'             Console.WriteLine("✗ " & resultado)
'         End If
'
'         System.Threading.Thread.Sleep(1000) ' Pausa de 1 segundo entre criações
'     Next
'
'     Console.WriteLine("=== RESUMO DA EXECUÇÃO ===")
'     Console.WriteLine(String.Format("Total processado: {0}", accountUIDs.Length))
'     Console.WriteLine(String.Format("Sucessos: {0}", sucessos))
'     Console.WriteLine(String.Format("Falhas: {0}", falhas))
'
'     ' Exemplo 3: Criar usuário com validação prévia e log detalhado
'     Dim targetUID As String = "UID-NOVO-USER"
'
'     Console.WriteLine("=== VALIDAÇÃO E CRIAÇÃO DE USUÁRIO ===")
'     Console.WriteLine("UID alvo: " & targetUID)
'
'     ' Primeiro verifica se a conta existe no OIM
'     Dim f = Session.SqlFormatter
'     Dim accountQuery = f.UidComparison("UID_UNSAccountB", targetUID)
'     Dim accountCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(accountQuery).SelectNonLobs)
'
'     If accountCol.Count > 0 Then
'         Dim account As IEntity = accountCol(0)
'         Dim revvoId As String = account.GetValue("CCC_CustomProperty24").String
'
'         Console.WriteLine("✓ Conta encontrada no OIM")
'
'         If String.IsNullOrEmpty(revvoId) Then
'             Console.WriteLine("→ Conta não possui ID do RevvoLearning. Iniciando criação...")
'             Dim resultado As String = CCC_RevvoLearning_CreateUser(clientId, clientSecret, targetUID)
'             Console.WriteLine("Resultado final: " & resultado)
'         Else
'             Console.WriteLine("→ Usuário já possui ID do RevvoLearning: " & revvoId)
'             Console.WriteLine("Resultado final: SUCESSO - Usuário já existe")
'         End If
'     Else
'         Console.WriteLine("✗ Conta não encontrada no OIM: " & targetUID)
'         Console.WriteLine("Resultado final: ERRO - Conta inexistente")
'     End If
'
'     Console.WriteLine("=== FIM DA EXECUÇÃO ===")
' End Sub
