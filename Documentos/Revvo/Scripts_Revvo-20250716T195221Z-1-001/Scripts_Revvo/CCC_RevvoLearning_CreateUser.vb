#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
#End If

Public Sub CCC_RevvoLearning_CreateUser(ByVal clientId As String, ByVal clientSecret As String, ByVal UID_UNSAccountB As String)
    Dim f = Session.SqlFormatter
    Dim queryUNSAccountB = f.UidComparison("UID_UNSAccountB", UID_UNSAccountB)
    Dim UNSAccountBCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(queryUNSAccountB).SelectNonLobs)
    
    If UNSAccountBCol.Count > 0 Then
        Dim UNSAccountB As IEntity = UNSAccountBCol(0)
        
        Dim username As String = UNSAccountB.GetValue("AccountName").String
        Dim firstname As String = UNSAccountB.GetValue("FirstName").String
        Dim lastname As String = UNSAccountB.GetValue("LastName").String
        Dim email As String = UNSAccountB.GetValue("CCC_CustomProperty15").String
        Dim password As String = ""
        
        Dim authToken As String = CCC_RevvoLearning_Auth(clientId, clientSecret)
        
        If String.IsNullOrEmpty(authToken) Then
            Return
        End If
        
        Dim jsonData As String = String.Format(
            "{{""user"": {{""username"": ""{0}"", ""firstname"": ""{1}"", ""lastname"": ""{2}"", ""email"": ""{3}"", ""password"": ""{4}"", ""createpassword"": ""True""}}}}",
            username, firstname, lastname, email, password
        )
        
        Try
            Using client As New WebClient()
                client.Headers.Add("Content-Type", "application/json")
                client.Headers.Add("Authorization", "Bearer " & authToken)
                
                Dim responseBytes As Byte() = client.UploadData("https://dasa-poc.learningflix.net/webservice/api/v3/users", "POST", System.Text.Encoding.ASCII.GetBytes(jsonData))
                Dim responseString As String = System.Text.Encoding.ASCII.GetString(responseBytes)
                
                Dim jsonResponse As JObject = JObject.Parse(responseString)
                Dim userId As String = jsonResponse("id").ToString()
                
                UNSAccountB.PutValue("CCC_CustomProperty24", userId)
                UNSAccountB.Save(Session)
                
            End Using
        Catch ex As Exception
            ' Erro na criação do usuário
        End Try
    End If
End Sub

' --- Exemplo de como usaria esta função em outro script ---
' Sub Main() ' Ou o contexto onde você chamaria esta função no One Identity Manager
'     ' Exemplo 1: Criar usuário para conta específica
'     Dim uidAccount As String = "UID-12345-ABCDE"
'     
'     Try
'         CCC_RevvoLearning_CreateUser(uidAccount)
'         Console.WriteLine("Processo de criação iniciado para: " & uidAccount)
'     Catch ex As Exception
'         Console.WriteLine("Erro ao criar usuário: " & ex.Message)
'     End Try
'     
'     ' Exemplo 2: Criar usuários em lote (para sincronização inicial)
'     Dim accountUIDs As String() = {"UID-001", "UID-002", "UID-003"}
'     
'     For Each uid As String In accountUIDs
'         Try
'             Console.WriteLine("Criando usuário para: " & uid)
'             CCC_RevvoLearning_CreateUser(uid)
'             System.Threading.Thread.Sleep(1000) ' Pausa de 1 segundo entre criações
'         Catch ex As Exception
'             Console.WriteLine("Falha ao criar " & uid & ": " & ex.Message)
'             ' Continua com próximo usuário mesmo se um falhar
'         End Try
'     Next
'     
'     ' Exemplo 3: Criar usuário com validação prévia
'     Dim targetUID As String = "UID-NOVO-USER"
'     
'     ' Primeiro verifica se a conta existe no OIM
'     Dim f = Session.SqlFormatter
'     Dim accountQuery = f.UidComparison("UID_UNSAccountB", targetUID)
'     Dim accountCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(accountQuery).SelectNonLobs)
'     
'     If accountCol.Count > 0 Then
'         Dim account As IEntity = accountCol(0)
'         Dim externalId As String = account.GetValue("UID_ExternalUser").String
'         
'         If String.IsNullOrEmpty(externalId) Then
'             Console.WriteLine("Conta encontrada sem ID externo. Criando usuário...")
'             CCC_RevvoLearning_CreateUser(targetUID)
'         Else
'             Console.WriteLine("Usuário já possui ID externo: " & externalId)
'         End If
'     Else
'         Console.WriteLine("Conta não encontrada no OIM: " & targetUID)
'     End If
' End Sub
