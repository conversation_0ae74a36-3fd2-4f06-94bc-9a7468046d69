#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
#End If

Public Sub CCC_RevvoLearning_DisableUser(ByVal clientId As String, ByVal clientSecret As String, ByVal UID_UNSAccount As String)
    Dim f = Session.SqlFormatter
    Dim personManagerAdsAccountQuery = f.UidComparison("UID_UNSAccountB", UID_UNSAccount)
    Dim personManagerAdsAccountCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(personManagerAdsAccountQuery).SelectNonLobs)
    
    If personManagerAdsAccountCol.Count > 0 Then
        Dim personManagerAdsAccount As IEntity = personManagerAdsAccountCol(0)
        Dim externalUserId As String = personManagerAdsAccount.GetValue("UID_ExternalUser").String
        
        If String.IsNullOrEmpty(externalUserId) Then
            Return
        End If
        
        Dim authToken As String = CCC_RevvoLearning_Auth(clientId, clientSecret)
        
        If String.IsNullOrEmpty(authToken) Then
            Return
        End If
        
        Dim jsonData As String = "{""user"": {""suspended"": true}}"
        
        Try
            Using client As New WebClient()
                client.Headers.Add("Content-Type", "application/json")
                client.Headers.Add("Authorization", "Bearer " & authToken)
                
                Dim responseBytes As Byte() = client.UploadData("https://dasa-poc.learningflix.net/webservice/api/v3/users/" & externalUserId, "PATCH", System.Text.Encoding.UTF8.GetBytes(jsonData))
                Dim responseString As String = System.Text.Encoding.UTF8.GetString(responseBytes)
                
            End Using
        Catch ex As Exception
            ' Erro ao desabilitar usuário
        End Try
    End If
End Sub

' --- Exemplo de como usaria esta função em outro script ---
' Sub Main() ' Ou o contexto onde você chamaria esta função no One Identity Manager
'     ' Exemplo 1: Desabilitar usuário específico
'     Dim uidAccount As String = "UID-12345-ABCDE"
'     
'     Try
'         CCC_RevvoLearning_DisableUser(uidAccount)
'         Console.WriteLine("Usuário desabilitado com sucesso: " & uidAccount)
'     Catch ex As Exception
'         Console.WriteLine("Erro ao desabilitar usuário: " & ex.Message)
'     End Try
'     
'     ' Exemplo 2: Desabilitar usuários inativos no OIM
'     Dim f = Session.SqlFormatter
'     Dim inactiveQuery = f.Comparison("IsInActive", "=", True, ValType.Bool)
'     Dim inactiveAccountsCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(inactiveQuery).SelectNonLobs)
'     
'     Console.WriteLine("Encontradas " & inactiveAccountsCol.Count & " contas inativas para desabilitar")
'     
'     For Each account As IEntity In inactiveAccountsCol
'         Dim uid As String = account.GetValue("UID_UNSAccountB").String
'         Dim externalId As String = account.GetValue("UID_ExternalUser").String
'         
'         If Not String.IsNullOrEmpty(externalId) Then
'             Try
'                 Console.WriteLine("Desabilitando: " & uid)
'                 CCC_RevvoLearning_DisableUser(uid)
'                 System.Threading.Thread.Sleep(500) ' Pausa entre operações
'             Catch ex As Exception
'                 Console.WriteLine("Falha ao desabilitar " & uid & ": " & ex.Message)
'             End Try
'         Else
'             Console.WriteLine("Pulando " & uid & " (sem ID externo)")
'         End If
'     Next
'     
'     ' Exemplo 3: Desabilitar com confirmação de status
'     Dim targetUID As String = "UID-TO-DISABLE"
'     
'     ' Primeiro verifica status atual no RevvoLearning
'     Dim currentStatus As String = CCC_RevvoLearning_GetUserByOIM(targetUID)
'     
'     If Not String.IsNullOrEmpty(currentStatus) Then
'         Dim statusJson As JObject = JObject.Parse(currentStatus)
'         Dim user As JObject = statusJson("user")
'         Dim isSuspended As Boolean = user("suspended").ToObject(Of Boolean)()
'         
'         If Not isSuspended Then
'             Console.WriteLine("Usuário está ativo. Desabilitando...")
'             CCC_RevvoLearning_DisableUser(targetUID)
'             
'             ' Verifica se desabilitação foi bem-sucedida
'             System.Threading.Thread.Sleep(2000) ' Aguarda processamento
'             Dim newStatus As String = CCC_RevvoLearning_GetUserByOIM(targetUID)
'             
'             If Not String.IsNullOrEmpty(newStatus) Then
'                 Dim newJson As JObject = JObject.Parse(newStatus)
'                 Dim newUser As JObject = newJson("user")
'                 Dim nowSuspended As Boolean = newUser("suspended").ToObject(Of Boolean)()
'                 
'                 If nowSuspended Then
'                     Console.WriteLine("✅ Usuário desabilitado com sucesso!")
'                 Else
'                     Console.WriteLine("⚠️ Usuário ainda aparece como ativo")
'                 End If
'             End If
'         Else
'             Console.WriteLine("Usuário já está desabilitado.")
'         End If
'     Else
'         Console.WriteLine("Não foi possível verificar status do usuário.")
'     End If
' End Sub
