#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
#End If

' Função de autenticação embarcada
Private Function CCC_RevvoLearning_Auth(ByVal clientId As String, ByVal clientSecret As String) As String
    Dim accessToken As String = Nothing
    
    Try
        Using client As New WebClient()
            client.Headers(HttpRequestHeader.ContentType) = "application/json"
            
            Dim jsonData As String = String.Format(
                "{{""grant_type"": ""client_credentials"", ""client_id"": ""{0}"", ""client_secret"": ""{1}""}}",
                clientId, clientSecret
            )
            
            Dim requestBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(jsonData)
            Dim responseBytes As Byte() = client.UploadData("https://dasa-poc.learningflix.net/webservice/api/oauth2/token", "POST", requestBytes)
            Dim responseString As String = System.Text.Encoding.UTF8.GetString(responseBytes)
            
            Dim jsonResponse As JObject = JObject.Parse(responseString)
            
            If jsonResponse IsNot Nothing AndAlso jsonResponse("access_token") IsNot Nothing Then
                accessToken = jsonResponse("access_token").ToString()
                Console.WriteLine("Token de acesso obtido com sucesso para RevvoLearning.")
            Else
                Console.WriteLine("Resposta JSON não contém 'access_token' ou está vazia.")
            End If
            
        End Using
        
    Catch ex As WebException
        Console.WriteLine("Erro na requisição web durante a autenticação RevvoLearning: " & ex.Message)
        Return Nothing
    Catch ex As Exception
        Console.WriteLine("Ocorreu um erro inesperado durante a autenticação RevvoLearning: " & ex.Message)
        Return Nothing
    End Try
    
    Return accessToken
End Function

Public Function CCC_RevvoLearning_GetUser(ByVal clientId As String, ByVal clientSecret As String, ByVal userId As String) As String
    Dim authToken As String = CCC_RevvoLearning_Auth(clientId, clientSecret)
    
    If String.IsNullOrEmpty(authToken) Then
        Return ""
    End If
    
    Dim apiUrl As String = "https://dasa-poc.learningflix.net/webservice/api/v3/users/" & userId
    
    Try
        Using client As New WebClient()
            client.Headers.Add("Authorization", "Bearer " & authToken)
            
            Dim responseString As String = client.DownloadString(apiUrl)
            Return responseString
        End Using
        
    Catch ex As Exception
        Return ""
    End Try
End Function

Public Function CCC_RevvoLearning_GetUserByOIM(ByVal clientId As String, ByVal clientSecret As String, ByVal UID_UNSAccount As String) As String
    Dim f = Session.SqlFormatter
    Dim personManagerAdsAccountQuery = f.UidComparison("UID_UNSAccountB", UID_UNSAccount)
    Dim personManagerAdsAccountCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(personManagerAdsAccountQuery).SelectNonLobs)
    
    If personManagerAdsAccountCol.Count > 0 Then
        Dim personManagerAdsAccount As IEntity = personManagerAdsAccountCol(0)
        Dim externalUserId As String = personManagerAdsAccount.GetValue("UID_ExternalUser").String
        
        If Not String.IsNullOrEmpty(externalUserId) Then
            Return CCC_RevvoLearning_GetUser(clientId, clientSecret, externalUserId)
        End If
    End If
    
    Return ""
End Function

' --- Exemplo de como usaria esta função em outro script ---
' Sub Main() ' Ou o contexto onde você chamaria esta função no One Identity Manager
'     ' Exemplo 1: Buscar usuário por ID externo
'     Dim userId As String = "12345"
'     Dim userJson As String = CCC_RevvoLearning_GetUser(userId)
'     
'     If Not String.IsNullOrEmpty(userJson) Then
'         Console.WriteLine("Usuário encontrado!")
'         Console.WriteLine("JSON Response: " & userJson)
'         
'         ' Parse para acessar dados específicos
'         Dim jsonResponse As JObject = JObject.Parse(userJson)
'         Dim user As JObject = jsonResponse("user")
'         
'         Console.WriteLine("Nome completo: " & user("firstname").ToString() & " " & user("lastname").ToString())
'         Console.WriteLine("Status: " & If(user("suspended").ToObject(Of Boolean)(), "Suspenso", "Ativo"))
'     Else
'         Console.WriteLine("Usuário não encontrado ou erro na consulta.")
'     End If
'     
'     ' Exemplo 2: Buscar usuário por UID do OIM
'     Dim oimUID As String = "UID-12345-67890"
'     Dim userByOIM As String = CCC_RevvoLearning_GetUserByOIM(oimUID)
'     
'     If Not String.IsNullOrEmpty(userByOIM) Then
'         Console.WriteLine("Usuário encontrado via OIM!")
'     Else
'         Console.WriteLine("Usuário não encontrado no OIM ou sem ID externo.")
'     End If
'     
'     ' Exemplo 3: Verificar se usuário existe antes de criar
'     Dim checkUserId As String = "98765"
'     Dim existingUser As String = CCC_RevvoLearning_GetUser(checkUserId)
'     
'     If String.IsNullOrEmpty(existingUser) Then
'         Console.WriteLine("Usuário não existe, pode criar novo.")
'         ' Chamar CCC_RevvoLearning_CreateUser aqui
'     Else
'         Console.WriteLine("Usuário já existe, pode atualizar.")
'         ' Chamar CCC_RevvoLearning_UpdateUser aqui
'     End If
' End Sub


