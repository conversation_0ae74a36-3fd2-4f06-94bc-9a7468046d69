#If Not SCRIPTDEBUGGER Then
References VI.DataImport.dll

Imports System.Collections.Generic
Imports System.IO
Imports System.Globalization
Imports VI.DB.Entities
Imports VI.DB.Specialized
Imports VI.DataImport
Imports Newtonsoft.Json.Linq
Imports Newtonsoft.Json
Imports System.Net
#End If

Public Sub CCC_RevvoLearning_UpdateUser(ByVal clientId As String, ByVal clientSecret As String, ByVal UID_UNSAccount As String)
    Dim f = Session.SqlFormatter
    Dim personManagerAdsAccountQuery = f.UidComparison("UID_UNSAccountB", UID_UNSAccount)
    Dim personManagerAdsAccountCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(personManagerAdsAccountQuery).SelectNonLobs)
    
    If personManagerAdsAccountCol.Count > 0 Then
        Dim personManagerAdsAccount As IEntity = personManagerAdsAccountCol(0)
        Dim externalUserId As String = personManagerAdsAccount.GetValue("UID_ExternalUser").String
        
        If String.IsNullOrEmpty(externalUserId) Then
            Return
        End If
        
        Dim username As String = personManagerAdsAccount.GetValue("AccountName").String
        Dim firstname As String = personManagerAdsAccount.GetValue("FirstName").String
        Dim lastname As String = personManagerAdsAccount.GetValue("LastName").String
        Dim email As String = personManagerAdsAccount.GetValue("Ident_PersonalEmail").String
        
        Dim authToken As String = CCC_RevvoLearning_Auth(clientId, clientSecret)
        
        If String.IsNullOrEmpty(authToken) Then
            Return
        End If
        
        Dim jsonData As String = String.Format(
            "{{""user"": {{""username"": ""{0}"", ""firstname"": ""{1}"", ""lastname"": ""{2}"", ""email"": ""{3}""}}}}",
            username, firstname, lastname, email
        )
        
        Try
            Using client As New WebClient()
                client.Headers.Add("Content-Type", "application/json")
                client.Headers.Add("Authorization", "Bearer " & authToken)
                
                Dim responseBytes As Byte() = client.UploadData("https://dasa-poc.learningflix.net/webservice/api/v3/users/" & externalUserId, "PATCH", System.Text.Encoding.UTF8.GetBytes(jsonData))
                Dim responseString As String = System.Text.Encoding.UTF8.GetString(responseBytes)
                
            End Using
        Catch ex As Exception
            ' Erro na atualização do usuário
        End Try
    End If
End Sub

' --- Exemplo de como usaria esta função em outro script ---
' Sub Main() ' Ou o contexto onde você chamaria esta função no One Identity Manager
'     ' Exemplo 1: Atualizar usuário específico
'     Dim uidAccount As String = "UID-12345-ABCDE"
'     
'     Try
'         CCC_RevvoLearning_UpdateUser(uidAccount)
'         Console.WriteLine("Usuário atualizado com sucesso: " & uidAccount)
'     Catch ex As Exception
'         Console.WriteLine("Erro ao atualizar usuário: " & ex.Message)
'     End Try
'     
'     ' Exemplo 2: Atualizar usuários modificados recentemente
'     Dim f = Session.SqlFormatter
'     Dim recentlyModified = f.Comparison("XDateUpdated", ">", DateTime.Now.AddDays(-1), ValType.Date)
'     Dim modifiedAccountsCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(recentlyModified).SelectNonLobs)
'     
'     Console.WriteLine("Encontradas " & modifiedAccountsCol.Count & " contas modificadas nas últimas 24h")
'     
'     For Each account As IEntity In modifiedAccountsCol
'         Dim uid As String = account.GetValue("UID_UNSAccountB").String
'         Dim externalId As String = account.GetValue("UID_ExternalUser").String
'         
'         If Not String.IsNullOrEmpty(externalId) Then
'             Try
'                 Console.WriteLine("Atualizando: " & uid)
'                 CCC_RevvoLearning_UpdateUser(uid)
'                 System.Threading.Thread.Sleep(500) ' Pausa entre atualizações
'             Catch ex As Exception
'                 Console.WriteLine("Falha ao atualizar " & uid & ": " & ex.Message)
'             End Try
'         Else
'             Console.WriteLine("Pulando " & uid & " (sem ID externo)")
'         End If
'     Next
'     
'     ' Exemplo 3: Atualizar com verificação de mudanças
'     Dim checkUID As String = "UID-CHECK-CHANGES"
'     
'     ' Busca dados atuais no RevvoLearning
'     Dim currentUserData As String = CCC_RevvoLearning_GetUserByOIM(checkUID)
'     
'     If Not String.IsNullOrEmpty(currentUserData) Then
'         Dim currentJson As JObject = JObject.Parse(currentUserData)
'         Dim currentUser As JObject = currentJson("user")
'         
'         ' Busca dados no OIM
'         Dim accountQuery = f.UidComparison("UID_UNSAccountB", checkUID)
'         Dim accountCol As IEntityCollection = Session.Source().GetCollection(Query.From("UNSAccountB").Where(accountQuery).SelectNonLobs)
'         
'         If accountCol.Count > 0 Then
'             Dim oimAccount As IEntity = accountCol(0)
'             Dim oimEmail As String = oimAccount.GetValue("Ident_PersonalEmail").String
'             Dim revvoEmail As String = currentUser("email").ToString()
'             
'             If oimEmail <> revvoEmail Then
'                 Console.WriteLine("Email mudou de '" & revvoEmail & "' para '" & oimEmail & "'. Atualizando...")
'                 CCC_RevvoLearning_UpdateUser(checkUID)
'             Else
'                 Console.WriteLine("Nenhuma mudança detectada para: " & checkUID)
'             End If
'         End If
'     End If
' End Sub



